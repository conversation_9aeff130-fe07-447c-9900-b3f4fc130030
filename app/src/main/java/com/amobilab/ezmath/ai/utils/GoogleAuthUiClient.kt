package com.amobilab.ezmath.ai.utils

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.common.utils.dlog
import android.content.Intent
import android.content.IntentSender
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.db.SignInResult
import com.amobilab.ezmath.ai.data.db.UserData
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.utils.FirestoreUtils.deleteCoinsForUser
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.BeginSignInRequest.GoogleIdTokenRequestOptions
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.ktx.auth
import com.google.firebase.auth.ktx.userProfileChangeRequest
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.tasks.await
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch

class GoogleAuthUiClient(
    private val oneTapClient: SignInClient,
    private val viewModel: MainDataViewModel,
) {
    private val auth: FirebaseAuth = Firebase.auth

    // Google sign-in
    suspend fun signIn(): IntentSender? {
        val result = try {
            oneTapClient.beginSignIn(
                buildSignInRequest()
            ).await()
        } catch (e: Exception) {
            e.printStackTrace()
            if (e is CancellationException) throw e
            null
        }
        return result?.pendingIntent?.intentSender
    }

    suspend fun signInWithIntent(intent: Intent): SignInResult {
        val credential = oneTapClient.getSignInCredentialFromIntent(intent)
        val googleIdToken = credential.googleIdToken ?: return SignInResult(
            data = null,
            errorMessage = "Google ID token is null"
        )
        PrefAssist.setString(PrefConst.TOKEN_ID, googleIdToken)
        val googleCredentials = GoogleAuthProvider.getCredential(googleIdToken, null)
        return try {
            val user = auth.signInWithCredential(googleCredentials).await().user
            user?.let {
                // Cập nhật tên hiển thị của người dùng nếu có
                val isNewUser = it.metadata?.creationTimestamp == it.metadata?.lastSignInTimestamp

                MixedUtils.showToast("userId: ${user.uid}")
//                FirestoreUtils.createCoinsForUser { currentCoins ->
//                    viewModel.coinViewModel.setCoinBalance(currentCoins)
//                }

                debugLog( "tài khoản mới cũ : $isNewUser")
                // Kết nối lại PowerSync với tài khoản mới
                try {
                    if (user.uid != PrefAssist.getString(PrefConst.USER_ID) && PrefAssist.getString(PrefConst.USER_ID).isNotEmpty()) {
                        AppDatabase.getInstance().disconnectAndClearForLogout()
                        debugLog("PowerSync đã ngắt kết nối và xóa dữ liệu khi đăng nhập với tài khoản mới: ${user.uid}")
                    }
                    PrefAssist.setString(PrefConst.USER_ID, user.uid)
                    AppDatabase.getInstance().connectToBackend()
                    debugLog("PowerSync đã kết nối lại với tài khoản mới: ${user.uid}")
                } catch (e: Exception) {
                    debugLog("Lỗi kết nối PowerSync: ${e.message}")
                }
            }
            SignInResult(
                data = user?.run {
                    UserData(
                        userId = uid,
                        username = displayName,
                        profilePictureUrl = photoUrl?.toString(),
                        phoneNumber = phoneNumber,
                        email = email
                    )
                },
                errorMessage = null
            )
        } catch (e: Exception) {
            e.printStackTrace()
            if (e is CancellationException) throw e
            SignInResult(
                data = null,
                errorMessage = e.message
            )
        }
    }

    //sent email verification
    fun sendEmailVerification(): SignInResult {
        return try {
            auth.currentUser?.sendEmailVerification()?.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                }
            }
            SignInResult(
                data = null,
                errorMessage = null
            )
        } catch (e: Exception) {
            e.printStackTrace()
            if (e is CancellationException) throw e
            SignInResult(
                data = null,
                errorMessage = e.message
            )
        }
    }

    // Sign up with email and password
    suspend fun signUpWithEmail(email: String, password: String, username: String): SignInResult {
        return try {
            val user = auth.createUserWithEmailAndPassword(email, password).await().user
            user?.let {
                // Cập nhật tên hiển thị của người dùng
                val profileUpdates = userProfileChangeRequest {
                    displayName = username
                }
                it.updateProfile(profileUpdates).await() // Đợi cập nhật hồ sơ hoàn tất

                PrefAssist.setString(PrefConst.USER_ID, user.uid)
//                FirestoreUtils.createCoinsForUser { currentCoins ->
//                    viewModel.coinViewModel.setCoinBalance(currentCoins)
//                }

                // Kết nối lại PowerSync với tài khoản mới
                viewModel.viewModelScope.launch {
                    try {
                        AppDatabase.getInstance().connectToBackend()
                        debugLog("PowerSync đã kết nối lại với tài khoản mới (Sign Up): ${user.uid}")
                    } catch (e: Exception) {
                        debugLog("Lỗi kết nối PowerSync (Sign Up): ${e.message}")
                    }
                }

            }
            SignInResult(
                data = user?.run {
                    UserData(
                        userId = uid,
                        username = displayName, // Sử dụng tên đã cập nhật
                        profilePictureUrl = photoUrl?.toString(),
                        phoneNumber = phoneNumber,
                        email = email
                    )
                },
                errorMessage = null
            )
        } catch (e: Exception) {
            e.printStackTrace()
            if (e is CancellationException) throw e
            SignInResult(
                data = null,
                errorMessage = e.message
            )
        }
    }

    // Sign in with email and password
    suspend fun signInWithEmail(email: String, password: String): SignInResult {
        return try {
            val user = auth.signInWithEmailAndPassword(email, password).await().user
            user?.let {
                PrefAssist.setString(PrefConst.USER_ID, user.uid)
                FirestoreUtils.createCoinsForUser { currentCoins ->
                    viewModel.coinViewModel.setCoinBalance(currentCoins)
                }

                // Kết nối lại PowerSync với tài khoản mới
                viewModel.viewModelScope.launch {
                    try {
                        AppDatabase.getInstance().connectToBackend()
                        debugLog("PowerSync đã kết nối lại với tài khoản mới (Email Sign In): ${user.uid}")
                    } catch (e: Exception) {
                        debugLog("Lỗi kết nối PowerSync (Email Sign In): ${e.message}")
                    }
                }
            }
            SignInResult(
                data = user?.run {
                    UserData(
                        userId = uid,
                        username = displayName,
                        profilePictureUrl = photoUrl?.toString(),
                        phoneNumber = phoneNumber,
                        email = email
                    )
                },
                errorMessage = null
            )
        } catch (e: Exception) {
            e.printStackTrace()
            if (e is CancellationException) throw e
            SignInResult(
                data = null,
                errorMessage = e.message
            )
        }
    }

    // Sign out
    suspend fun signOut() {
        try {
            viewModel.coinViewModel.setCoinBalance(0)

            // Ngắt kết nối và xóa dữ liệu PowerSync trước khi đăng xuất
            viewModel.viewModelScope.launch {
                try {
                    AppDatabase.getInstance().disconnectAndClearForLogout()
                    debugLog("PowerSync đã ngắt kết nối và xóa dữ liệu khi đăng xuất")
                } catch (e: Exception) {
                    debugLog("Lỗi ngắt kết nối PowerSync khi đăng xuất: ${e.message}")
                }
            }

            oneTapClient.signOut().await()
            auth.signOut()

            // Xóa USER_ID để PowerSync không thể lấy token cho user cũ
            PrefAssist.setString(PrefConst.USER_ID, "")
            debugLog("Đã xóa USER_ID khi đăng xuất")
        } catch (e: Exception) {
            e.printStackTrace()
            if (e is CancellationException) throw e
        }
    }

    // Get signed-in user
    fun getSignedInUser(): UserData? = auth.currentUser?.run {
        UserData(
            userId = uid,
            username = displayName,
            profilePictureUrl = photoUrl?.toString(),
            phoneNumber = phoneNumber,
            email = email
        )
    }

    // Build Google sign-in request
    private fun buildSignInRequest(): BeginSignInRequest {
        return BeginSignInRequest.Builder()
            .setGoogleIdTokenRequestOptions(
                GoogleIdTokenRequestOptions.builder()
                    .setSupported(true)
                    .setFilterByAuthorizedAccounts(false)
                    .setServerClientId(
                        CommApplication.appContext.getString(R.string.web_client_id)
                    )
                    .build()
            )
            .setAutoSelectEnabled(true)
            .build()
    }

    suspend fun deleteAccount(): Boolean {
        try {
            val currentUser = auth.currentUser
            if (currentUser == null) {
                return false
            }

            val idToken = PrefAssist.getString(PrefConst.TOKEN_ID)
            dlog("idToken: $idToken")
            // Re-authenticate user
            val credential = GoogleAuthProvider.getCredential(idToken, null)

            currentUser.reauthenticate(credential).await()


            deleteCoinsForUser(currentUser.uid)

            AppDatabase
                .getInstance()
                .getUserRepository()
                .updateIsDelete(
                    true
                )

            // Delete user from Firebase Authentication
            currentUser.delete().await()

            // Delete user document from Firestore
            PrefAssist.setString(PrefConst.TOKEN_ID, "")
            PrefAssist.setString(PrefConst.USER_ID, "")
            debugLog("Đã xóa USER_ID và TOKEN_ID khi xóa tài khoản")
            return true

        } catch (e: Exception) {
            e.printStackTrace()
            debugLog("Error: ${e.message}")
            return false // Error occurred
        }
    }


    suspend fun checkAccountValidity(): Boolean? {
        return try {
            val currentUser = auth.currentUser
            if (currentUser == null) {
                return true // Người dùng không đăng nhập
            }

            // Kiểm tra xem tài liệu người dùng có tồn tại trong Firestore không
            val userExistsInFirestore = FirestoreUtils.checkUserDocumentExists(currentUser.uid)

            // Trả về kết quả
            userExistsInFirestore
        } catch (e: Exception) {
            e.printStackTrace()
            null // Xảy ra lỗi
        }
    }
}
